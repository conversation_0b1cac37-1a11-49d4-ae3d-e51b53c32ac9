import 'package:bibl/controllers/auth_controller.dart';
import 'package:bibl/views/auth_screens/forgot_password.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../res/style.dart';
import '../../widgets/customappbar.dart';
import '../../widgets/custombutton.dart';
import 'dart:io' show Platform;

import 'email_register.dart';

class Login extends StatefulWidget {
  const Login({super.key});

  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();

  final AuthController authController = Get.find<AuthController>();

  bool isPasswordObscure = true;
  bool value1 = false;

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  login() {
    if (emailController.text.isNotEmpty && passwordController.text.isNotEmpty) {
      authController.login(
          emailController.text.trim(), passwordController.text.trim());
    } else {
      getErrorSnackBar(errorMessage);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        widget: null,
        title: 'Dobro Došli!',
      ),
      body: SafeArea(
        child: ScrollConfiguration(
          behavior: const ScrollBehavior(),
          child: SingleChildScrollView(
            child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16.0),
                const Txt(
                  txt: 'Prijavite se na svoj profil.',
                  fontSize: 16,
                ),
                const SizedBox(height: 20.0),
                // Email Address Field
                const Txt(
                  txt: 'Email Adresa',
                  fontSize: 14,
                ),

                const SizedBox(height: 8.0),
                textFieldContainer(
                  context,
                  controller: emailController,
                  prefix: const Icon(
                    Icons.email_outlined,
                    color: grey2Color,
                  ),
                  hint: 'Unesite Email Adresu',
                ),

                const SizedBox(height: 20.0),
                // Password Field
                const Txt(
                  txt: 'Lozinka',
                  fontSize: 14,
                ),

                const SizedBox(height: 8.0),
                textFieldContainer(
                  context,
                  controller: passwordController,
                  hint: 'Unesite Vašu Lozinku',
                  prefix: const Icon(
                    Icons.lock_outline,
                    color: grey2Color,
                  ),
                  maxLines: 1,
                  isObscure: isPasswordObscure ? true : null,
                  trailing: IconButton(
                    icon: Icon(
                      isPasswordObscure
                          ? Icons.visibility_off
                          : Icons.visibility,
                      color: grey2Color,
                    ),
                    onPressed: () {
                      setState(() {
                        isPasswordObscure = !isPasswordObscure;
                      });
                    },
                  ),
                ),

                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () {
                      Get.to(() => const ForgotPassword(),
                          transition: Transition.rightToLeft,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut);
                    },
                    child: const Txt(
                      txt: 'Zaboravili ste vašu lozinku?',
                      fontSize: 12,
                      fontWeight: FontWeight.normal,
                      fontColor: mainColor,
                    ),
                  ),
                ),
                const SizedBox(height: 16.0),
                // Login Button
                Obx(
                  () => buttonContainer(
                      text: 'Prijavite se',
                      child: authController.isAuthUpdating.isTrue
                          ? const Center(
                              child: CircularProgressIndicator(
                                color: Colors.white,
                              ),
                            )
                          : null,
                      onTap: login),
                ),

                // const SizedBox(height: 20.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Txt(
                      txt: 'Nemate još nalog?',
                      fontSize: 12,
                      fontWeight: FontWeight.normal,
                    ),
                    TextButton(
                      onPressed: () {
                        Get.to(() => const EmailRegister(),
                            transition: Transition.rightToLeft,
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut);
                      },
                      child: const Txt(
                        txt: 'Registrujte se!',
                        fontSize: 12,
                        fontWeight: FontWeight.normal,
                        fontColor: mainColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 30.0),
                const Center(
                  child: Txt(
                    txt: 'ILI',
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                  ),
                ),
                const SizedBox(height: 20.0),
                // Social Media Icons
                Row(
                  children: [
                    Expanded(child: Container()),
                    const Expanded(child: Divider()),
                    const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Txt(
                        txt: 'koristi neku od platformi',
                        fontSize: 14,
                        fontColor: grey2Color,
                      ),
                    ),
                    const Expanded(child: Divider()),
                    Expanded(child: Container()),
                  ],
                ),
                const SizedBox(height: 10.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Obx(
                      () => socialMediaLoginButtons(
                        authController.isGoogleAuthUpdating.isTrue
                            ? const Center(
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                ),
                              )
                            : SvgPicture.asset(
                                'assets/svgs/google_icon.svg',
                                height: 40,
                              ),
                        () {
                          authController.googleLogin();
                        },
                      ),
                    ),
                    const SizedBox(width: 20.0),
                    Platform.isIOS
                        ? Obx(() => socialMediaLoginButtons(
                              authController.isAppleAuthUpdating.isTrue
                                  ? const Center(
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                      ),
                                    )
                                  : SvgPicture.asset(
                                      'assets/svgs/apple_icon.svg',
                                      height: 40,
                                    ),
                              () {
                                authController.appleLogin();
                              },
                            ))
                        : const SizedBox.shrink(),
                  ],
                ),
              ],
            ),
          ),
        ),
        ),
      ),
    );
  }

  GestureDetector socialMediaLoginButtons(Widget widget, Function() onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(
            8.0), // Adjust padding to increase the border thickness
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: grey2Color, // Replace with your desired border color
            width: 1.0, // Adjust border width
          ),
        ),
        child: ClipOval(child: widget),
      ),
    );
  }
}
