import 'package:bibl/models/league_user_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/profile_controller.dart';

class LeagueService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Adds a user to a specific league and group
  Future<void> addUserToLeague(
    String league,
    String groupId,
    String userId,
    String username,
    ProfileController profileController,
  ) async {
    try {
      final groupRef = _firestore
          .collection('leaderboards')
          .doc(league)
          .collection('groups')
          .doc(groupId);
      final playerDocRef = groupRef.collection('players').doc(userId);

      await _firestore.runTransaction((transaction) async {
        final groupDoc = await transaction.get(groupRef);
        final playerDoc = await transaction.get(playerDocRef);

        if (!groupDoc.exists) {
          transaction.set(groupRef, {
            'createdAt': Timestamp.now(),
            'league': league,
          });
        }

        if (!playerDoc.exists) {
          transaction.set(
            playerDocRef,
            LeaguePlayerModel(
              groupId: groupId,
              username: username.isNotEmpty
                  ? username
                  : profileController.userr.value.uniqueName ?? 'Unknown',
              league: league,
              weeklyScore: 0,
              playerId: userId,
              lastUpdated: Timestamp.now(),
            ).toJson(),
          );
        }
      });

      // Update users collection
      await _updateUserDocument(userId, league, groupId);
      _updateProfileController(profileController, league, groupId);
    } catch (e) {
      debugPrint('Error adding user to league: $e');
      Get.snackbar('Error', 'Failed to assign user to league: $e');
      rethrow;
    }
  }

  /// Assigns a user to the Bronzana league, finding or creating an appropriate group
  Future<void> assignUserToBronzana(
    String userId,
    String username,
    ProfileController profileController,
  ) async {
    try {
      const league = 'Bronzana';
      final groupId = await _findOrCreateGroup(league);
      await addUserToLeague(
          league, groupId, userId, username, profileController);
    } catch (e) {
      debugPrint('Error assigning user to Bronzana: $e');
      Get.snackbar('Error', 'Failed to assign to Bronzana League: $e');
      rethrow;
    }
  }

  /// Finds an available group or creates a new one
  Future<String> _findOrCreateGroup(String league) async {
    final groupsSnapshot = await _firestore
        .collection('leaderboards')
        .doc(league)
        .collection('groups')
        .get();

    // Check for existing group with less than 10 players
    for (var groupDoc in groupsSnapshot.docs) {
      final playersSnapshot =
          await groupDoc.reference.collection('players').get();
      if (playersSnapshot.docs.length < 10) {
        return groupDoc.id;
      }
    }

    // Create new group if none found
    final groupCount = groupsSnapshot.docs.length + 1;
    return 'group_$groupCount';
  }

  /// Updates the user document with league information
  Future<void> _updateUserDocument(
      String userId, String league, String groupId) async {
    await _firestore.collection('users').doc(userId).update({
      'league': league,
      'groupId': groupId,
      'lastUpdated': Timestamp.now(),
    });
  }

  /// Updates the profile controller with league information
  void _updateProfileController(
    ProfileController profileController,
    String league,
    String groupId,
  ) {
    profileController.userr.value.league = league;
    profileController.userr.value.groupId = groupId;
    profileController.userr.refresh();
  }

  /// Checks if a user exists in a league
  bool isUserInLeague(List<LeaguePlayerModel> players, String userId) {
    return players.any((player) => player.playerId == userId);
  }

  /// Gets the leaderboard stream for a specific league and group
  Stream<QuerySnapshot> getLeaderboardStream(String league, String groupId) {
    return _firestore
        .collection('leaderboards')
        .doc(league)
        .collection('groups')
        .doc(groupId)
        .collection('players')
        .orderBy('weeklyScore', descending: true)
        .orderBy('lastUpdated', descending: false)
        .snapshots();
  }
}
