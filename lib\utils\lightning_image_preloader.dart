import 'dart:async';
import 'package:flutter/material.dart';
import 'enhanced_cache_manager.dart';

/// Lightning-fast image preloading system for instant image display
class LightningImagePreloader {
  static final Map<String, bool> _preloadedImages = {};
  static final Set<String> _currentlyPreloading = {};
  static const int _maxConcurrentPreloads = 8;
  static final List<String> _preloadQueue = [];
  static bool _isProcessingQueue = false;

  /// Preload images immediately when they come into view
  static Future<void> preloadImagesInBatch(List<String> imageUrls) async {
    final urlsToPreload = imageUrls
        .where((url) => !_preloadedImages.containsKey(url) && !_currentlyPreloading.contains(url))
        .take(10) // Limit batch size
        .toList();

    if (urlsToPreload.isEmpty) return;

    // Add to queue for processing
    _preloadQueue.addAll(urlsToPreload);
    _processPreloadQueue();
  }

  static void _processPreloadQueue() async {
    if (_isProcessingQueue || _preloadQueue.isEmpty) return;
    
    _isProcessingQueue = true;
    
    while (_preloadQueue.isNotEmpty && _currentlyPreloading.length < _maxConcurrentPreloads) {
      final url = _preloadQueue.removeAt(0);
      if (!_preloadedImages.containsKey(url) && !_currentlyPreloading.contains(url)) {
        _preloadSingleImage(url);
      }
    }
    
    _isProcessingQueue = false;
    
    // Continue processing if there are more items
    if (_preloadQueue.isNotEmpty) {
      Timer(const Duration(milliseconds: 100), _processPreloadQueue);
    }
  }

  static void _preloadSingleImage(String url) async {
    _currentlyPreloading.add(url);
    
    try {
      final file = await EnhancedCacheManager.ultraFastPreload(url);
      _preloadedImages[url] = file != null;
    } catch (e) {
      _preloadedImages[url] = false;
    } finally {
      _currentlyPreloading.remove(url);
      // Continue queue processing
      if (_preloadQueue.isNotEmpty) {
        _processPreloadQueue();
      }
    }
  }

  /// Preload images that are likely to be viewed next (smart preloading)
  static Future<void> smartPreload(List<String> visibleUrls, List<String> nextUrls) async {
    // Immediately preload visible images
    await preloadImagesInBatch(visibleUrls);
    
    // Queue next images for background preloading
    Timer(const Duration(milliseconds: 500), () {
      preloadImagesInBatch(nextUrls);
    });
  }

  /// Check if image is already preloaded
  static bool isPreloaded(String url) {
    return _preloadedImages[url] == true;
  }

  /// Preload images for a specific screen/widget
  static Future<void> preloadForScreen(String screenName, List<String> imageUrls) async {
    debugPrint('🚀 Preloading ${imageUrls.length} images for $screenName');
    await preloadImagesInBatch(imageUrls);
  }

  /// Clear preload cache to free memory
  static void clearPreloadCache() {
    _preloadedImages.clear();
    _currentlyPreloading.clear();
    _preloadQueue.clear();
  }

  /// Get preload statistics
  static Map<String, dynamic> getStats() {
    return {
      'preloaded': _preloadedImages.length,
      'currently_preloading': _currentlyPreloading.length,
      'queue_size': _preloadQueue.length,
    };
  }
}