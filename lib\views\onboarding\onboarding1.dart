import 'package:bibl/controllers/category_controller.dart';
import 'package:bibl/models/category_model.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/views/onboarding/onboarding2.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../widgets/category_icon_widget.dart';
import '../../widgets/customappbar.dart';

class OnBoarding1 extends StatefulWidget {
  const OnBoarding1({super.key});

  @override
  State<OnBoarding1> createState() => _Onboarding1State();
}

class _Onboarding1State extends State<OnBoarding1> {
  final CategoryController categoryController = Get.find();
  final ValueNotifier<List<String>> selectedCategories = ValueNotifier([]);
  final List<String> allCategoryNames = [];

  @override
  void initState() {
    super.initState();

    if (categoryController.allCategories.isEmpty) {
      categoryController.fetchAllCategories();
    }

    allCategoryNames.assignAll(categoryController.allCategories
        .map((category) => category.topicName ?? '')
        .toList());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        widget: null,
        isBackButton: false,
        title: 'Šta te najbolje opisuje',
      ),
      body: Obx(
        () => SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 20),
                categoryController.allCategories.isEmpty
                    ? const Expanded(
                        child: Center(
                          child: CircularProgressIndicator(
                            color: mainColor,
                          ),
                        ),
                      )
                    : Expanded(
                        child: GridView.builder(
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            mainAxisSpacing: 10,
                            crossAxisSpacing: 10,
                            childAspectRatio: 2.6,
                          ),
                          itemCount: categoryController.allCategories.length,
                          itemBuilder: (context, index) {
                            final category =
                                categoryController.allCategories[index];
                            return GridItem(
                              category: category,
                              selectedCategories: selectedCategories,
                            );
                          },
                        ),
                      ),
                ValueListenableBuilder<List<String>>(
                  valueListenable: selectedCategories,
                  builder: (context, selected, child) {
                    return buttonContainer(
                      isForOnboarding: selected.length < 5 ? true : null,
                      text: 'NASTAVI (${selected.length}/5)',
                      onTap: selected.length < 5
                          ? () {}
                          : () {
                              if (selected.length > 4) {
                                Get.to(() => const Onboarding2());
                                categoryController
                                    .addSelectedCategoriesToDatabase(selected);
                                categoryController
                                    .addSelectedLibraryCategoriesToDatabase(
                                        allCategoryNames);
                              }
                            },
                    );
                  },
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class GridItem extends StatefulWidget {
  final CategoryModel category;
  final ValueNotifier<List<String>> selectedCategories;

  const GridItem({
    Key? key,
    required this.category,
    required this.selectedCategories,
  }) : super(key: key);

  @override
  State<GridItem> createState() => _GridItemState();
}

class _GridItemState extends State<GridItem> {
  late bool isSelected;

  final CategoryController categoryController = Get.find<CategoryController>();

  @override
  void initState() {
    super.initState();
    isSelected = widget.selectedCategories.value
        .contains(widget.category.topicName ?? '');
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        final topicName = widget.category.topicName ?? '';
        final currentSelection = widget.selectedCategories.value;

        setState(() {
          if (currentSelection.contains(topicName)) {
            widget.selectedCategories.value = List.from(currentSelection)
              ..remove(topicName);
          } else if (currentSelection.length < 5) {
            widget.selectedCategories.value = List.from(currentSelection)
              ..add(topicName);
          }
          isSelected = widget.selectedCategories.value.contains(topicName);
        });
      },
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xffF8EFFF) : Colors.white,
          border: Border.all(
            color: isSelected
                ? const Color(0xff9610FF).withValues(alpha: 0.5)
                : Colors.black.withValues(alpha: 0.2),
          ),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              Expanded(
                child: CustomCategoryIcon(
                  topicName: widget.category.topicName ?? '',
                  topicPhotoLink: widget.category.topicPhotoLink ?? '',
                  isSelected: isSelected,
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                flex: 3,
                child: Txt(
                  txt: widget.category.topicName ?? '',
                  fontSize: 14,
                  fontWeight: FontWeight.normal,
                  maxLines: 3,
                  fontColor: isSelected ? mainColor : Colors.black,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
