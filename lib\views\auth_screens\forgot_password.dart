import 'package:bibl/controllers/auth_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../widgets/customappbar.dart';

class ForgotPassword extends StatefulWidget {
  const ForgotPassword({super.key});

  @override
  State<ForgotPassword> createState() => _ForgotPasswordState();
}

class _ForgotPasswordState extends State<ForgotPassword> {
  final emailController = TextEditingController();
  bool isEmailvalid = false;
  final AuthController authController = Get.find<AuthController>();

  @override
  void dispose() {
    emailController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        widget: null,
        title: '<PERSON>ab<PERSON><PERSON> ste lozinku',
      ),
      body: <PERSON><PERSON><PERSON>(
        child: ScrollConfiguration(
          behavior: const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(),
          child: SingleChildScrollView(
            child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Row(
                      children: [
                        Txt(
                          txt: 'Unesite svoju email adresu',
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontColor: lightColor,
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    emailWidget(context),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    buttonContainer(
                      onTap: () {
                        if (emailController.text.isNotEmpty) {
                          if (isEmailvalid) {
                            authController.forgorPassword(
                                emailController.value.text.trim());
                          } else {
                            getErrorSnackBar('Unesite ispravnu email adresu');
                          }
                        } else {
                          getErrorSnackBar(
                            "Molimo unesite email adresu prvo",
                          );
                        }
                      },
                      text: 'Pošalji',
                    ),
                    SizedBox(
                      height: Get.height * 0.03,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        ),
      ),
    );
  }

  Widget emailWidget(BuildContext context) {
    return textFieldContainer(context,
        controller: emailController,
        labelText: 'Email',
        prefix: const Padding(
          padding: EdgeInsets.all(12.0),
          child: Icon(
            Icons.email_outlined,
            color: grey2Color,
          ),
        ),
        padding: const EdgeInsets.fromLTRB(12, 5, 12, 15), onChanged: (val) {
      RegExp emailRegEx = RegExp(
          r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$');

      if (emailRegEx.hasMatch(val)) {
        setState(() {
          isEmailvalid = true;
        });
      } else {
        setState(() {
          isEmailvalid = false;
        });
      }
    },
        trailing: isEmailvalid
            ? const Padding(
                padding: EdgeInsets.only(top: 10, bottom: 10),
                child: CircleAvatar(
                  backgroundColor: mainColor,
                  maxRadius: 5,
                  child: Center(
                    child: Icon(
                      Icons.check,
                      size: 16,
                      color: Colors.white,
                    ),
                  ),
                ),
              )
            : const SizedBox(
                width: 10,
              ));
  }
}
