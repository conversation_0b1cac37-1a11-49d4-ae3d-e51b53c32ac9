import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:permission_handler/permission_handler.dart';

class NotificationService {
  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<bool> initNotification() async {
    // Request notification permissions first
    bool permissionGranted = await requestNotificationPermissions();
    
    if (!permissionGranted) {
      return false; // Permission denied
    }
    
    var initializationSettingsAndroid =
        const AndroidInitializationSettings('@mipmap/ic_launcher');
    var initializationSettingsIOS = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
      onDidReceiveLocalNotification:
          (int id, String? title, String? body, String? payload) async {},
    );
    var initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    await flutterLocalNotificationsPlugin.initialize(initializationSettings);
    return true; // Successfully initialized
  }

  Future<bool> requestNotificationPermissions() async {
    final status = await Permission.notification.status;
    if (status != PermissionStatus.granted) {
      final result = await Permission.notification.request();
      return result == PermissionStatus.granted;
    }
    return true; // Already granted
  }

  cancelNotification() async {
    await flutterLocalNotificationsPlugin.cancelAll();
  }

  notificationDetails() {
    return const NotificationDetails(
        android: AndroidNotificationDetails(
            "umnilab_notifications", 
            "UmniLab Notifications",
            channelDescription: "Notifications for UmniLab app",
            styleInformation: BigTextStyleInformation(''),
            priority: Priority.max,
            importance: Importance.max,
            enableLights: true,
            enableVibration: true,
            playSound: true,
            showWhen: true),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ));
  }

  showNotification({required String title, required String body}) async {
    await flutterLocalNotificationsPlugin.show(
      1, // Unique ID for the notification
      title,
      body,
      notificationDetails(),
    );
  }

  Future<void> scheduleNotification() async {
    final String currentTimeZone = await FlutterTimezone.getLocalTimezone();

    tz.setLocalLocation(tz.getLocation(currentTimeZone));

    final tz.TZDateTime scheduledNotificationDateTime = tz.TZDateTime.from(
        DateTime.now().add(const Duration(days: 6)), tz.local);
    // DateTime.now().add(const Duration(seconds: 30)),
    // tz.local);

    await flutterLocalNotificationsPlugin.zonedSchedule(
      1, // Unique ID for the notification
      'Vaša probna verzija ističe za 24 sata.',
      null,
      scheduledNotificationDateTime,
      notificationDetails(),
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }
}
