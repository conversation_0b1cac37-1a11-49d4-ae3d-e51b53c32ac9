import 'package:bibl/controllers/auth_controller.dart';
import 'package:bibl/views/auth_screens/login.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../res/style.dart';
import '../../widgets/customappbar.dart';
import '../../widgets/custombutton.dart';
import 'dart:io' show Platform;
import 'email_register.dart';

class Authentication extends StatefulWidget {
  const Authentication({super.key});

  @override
  State<Authentication> createState() => _AuthenticationState();
}

class _AuthenticationState extends State<Authentication> {
  final authController = Get.find<AuthController>();

  buildSocialLoginButton({
    required VoidCallback onTap,
    required String assetPath,
    required String buttonText,
    required Rx<bool> isLoading,
  }) {
    return Obx(
      () => buttonContainer(
        onTap: onTap,
        child: isLoading.value
            ? const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              )
            : Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(assetPath, height: 40),
                    const SizedBox(
                      width: 10,
                    ),
                    Flexible(
                      child: Txt(
                        txt: buttonText,
                        fontWeight: FontWeight.w600,
                        fontColor: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        widget: null,
        isBackButton: false,
        title: 'Dobro Došli!',
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Spacer(flex: Platform.isIOS ? 10 : 8),
              Image.asset(
                'assets/images/auth_logo.png',
                height: 150,
              ),
              // Image.asset(
              //   'assets/svgs/image.png',
              //   height: 100,
              // ),
              Spacer(flex: Platform.isIOS ? 10 : 8),
              if (Platform.isIOS)
                buildSocialLoginButton(
                  onTap: authController.appleLogin,
                  isLoading: authController.isAppleAuthUpdating,
                  assetPath: 'assets/svgs/apple_icon2.svg',
                  buttonText: 'Ulogujte se preko Apple-a',
                ),
              const Spacer(),
              buildSocialLoginButton(
                onTap: authController.googleLogin,
                assetPath: 'assets/svgs/google_icon2.svg',
                buttonText: 'Ulogujte se preko Google-a',
                isLoading: authController.isGoogleAuthUpdating,
              ),
              const Spacer(),
              outlineButtonContainer(
                onTap: () => Get.to(() => const EmailRegister(),
                    transition: Transition.rightToLeft,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut),
                child: const Center(
                  child: Txt(
                    txt: 'Registrujte Se',
                    fontSize: 16,
                    fontColor: mainColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const Spacer(),
              outlineButtonContainer(
                onTap: () => Get.to(() => const Login(),
                    transition: Transition.rightToLeft,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut),
                child: const Center(
                  child: Txt(
                    txt: 'Prijavite se',
                    fontSize: 16,
                    fontColor: mainColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }
}
