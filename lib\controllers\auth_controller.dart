import 'dart:async';
import 'dart:math';
import 'package:bibl/bnb.dart';
import 'package:bibl/controllers/heart_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/controllers/lesson_controller.dart';
import 'package:bibl/controllers/quiz_controller.dart';
import 'package:bibl/models/user_model.dart';
import 'package:bibl/views/auth_screens/auth.dart';
import 'package:bibl/views/onboarding/onboarding1.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart'
    show FirebaseMessaging;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../res/style.dart';
import 'payment_controller.dart';
import '../utils/enhanced_cache_manager.dart';

class AuthController extends GetxController {
  static AuthController instance = Get.find();
  Rx<bool> isAuthUpdating = false.obs;
  Rx<bool> isSigningOut = false.obs;
  Rx<bool> isThereDeepLink = true.obs;
  Rx<bool> isGoogleAuthUpdating = false.obs;
  Rx<bool> isAppleAuthUpdating = false.obs;
  Rx<int> loginAttempts = 0.obs;
  Rx<int> endTime = 0.obs;

  late Rx<User?> _user;
  bool isLoging = false;
  User? get user => _user.value;
  final _auth = FirebaseAuth.instance;
  RxList<UserModel> allUsersList = <UserModel>[].obs;

  Rx<int> isObscure = 1.obs;

  // Stream subscription for proper cleanup
  StreamSubscription<User?>? _authSubscription;

  @override
  void onInit() {
    super.onInit();
    // Initialize user immediately to prevent LateInitializationError
    _user = Rx<User?>(_auth.currentUser);
    _authSubscription = _auth.authStateChanges().listen((user) {
      _user.value = user;
    });
  }

  @override
  void onReady() {
    super.onReady();
    // Don't use automatic redirect on startup to allow splash controller to handle it
    // Only bind for subsequent auth changes
    if (_auth.currentUser != null) {
      ever(_user, loginRedirect);
    }
  }

  @override
  void onClose() {
    _authSubscription?.cancel();
    _clearAllUserData();
    super.onClose();
  }

  /// Clear all user-specific data
  void _clearAllUserData() {
    allUsersList.clear();
    isAuthUpdating.value = false;
    isSigningOut.value = false;
    isGoogleAuthUpdating.value = false;
    isAppleAuthUpdating.value = false;
    loginAttempts.value = 0;
    endTime.value = 0;
    isObscure.value = 1;
    isLoging = false;
  }

  // Enable auth redirect for subsequent auth changes
  void enableAuthRedirect() {
    ever(_user, loginRedirect);
  }

  Future<void> loginRedirect(var user) async {
    isAuthUpdating.value = true;

    // Remove unnecessary delay for better performance
    if (!isLoging) {
      await Future.delayed(const Duration(milliseconds: 500));
    }

    if (_auth.currentUser == null) {
      isAuthUpdating.value = false;
      // Use smooth transition instead of offAll
      Get.offAll(() => const Authentication(),
          transition: Transition.fadeIn,
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeInOut);
    } else {
      final userDoc = await firestore.collection('users').doc(user.uid).get();
      bool hasSeenOnboarding =
          userDoc.data()?['hasSeenOnboadingScreen'] ?? false;

      // Force reload ProfileController data for new user
      await ProfileController.forceReloadUserData();

      // Use smooth transition with fade
      if (hasSeenOnboarding) {
        Get.offAll(() => const BNB(),
            transition: Transition.fadeIn,
            duration: const Duration(milliseconds: 400),
            curve: Curves.easeInOut);
      } else {
        Get.offAll(() => const OnBoarding1(),
            transition: Transition.fadeIn,
            duration: const Duration(milliseconds: 400),
            curve: Curves.easeInOut);
      }

      await PurchaseApi.init();
      isAuthUpdating.value = false;
    }
  }

  Future<void> generateUniqueUsername(String uid, String name) async {
    final usersRef = FirebaseFirestore.instance.collection('users');
    final baseName = name.toLowerCase().replaceAll(' ', '');

    int suffix = 1;
    String uniqueUsername;

    while (true) {
      uniqueUsername = '@$baseName-$suffix';

      final existing =
          await usersRef.where('uniqueName', isEqualTo: uniqueUsername).get();

      if (existing.docs.isEmpty) {
        break;
      }

      suffix++;
    }

    // Save to Firestore
    await usersRef.doc(uid).update({
      'uniqueName': uniqueUsername,
    });
  }

  void registerUser({
    email,
    password,
    name,
    surname,
  }) async {
    final HeartController heartController = Get.find();
    isAuthUpdating.value = true;
    String errorMessage = '';
    try {
      isLoging = true;

      UserCredential cred = await _auth.createUserWithEmailAndPassword(
          email: email, password: password);
      List<String> imgList = [
        'assets/images/img1.png',
        'assets/images/img2.png',
        'assets/images/img3.png',
        'assets/images/img4.png',
      ];
      Random random = Random();
      String randomImage = imgList[random.nextInt(imgList.length)];

      UserModel user = UserModel(
        email: email,
        profilePhoto: randomImage,
        surname: surname,
        name: name,
        uid: cred.user!.uid,
        neurons: 0,
        lastListResetTimestamp: Timestamp.now(),
        lastStreakUpdate: Timestamp.now(),
        lastActiveTime: Timestamp.now(),
        weeklyStreak: 0,
        hearts: heartController.defaultsHearts.value,
        listOfFavCategories: [],
        listOfLibraryCategories: [],
        achievements: [],
        appOpenCount: 0,
        completedArticleIds: [],
        hasSeenOnboadingScreen: false,
        hasSeenInfoSheet: false,
        consecutiveStreak: 1,
        completedQuizIds: [],
        consecutivePerfectQuizzes: 0,
        correctQuizAnswersCounter: 0,
        hasReachedGoldLeague: false,
        hasReachedSilverLeague: false,
        perfectQuizStreak: 0,
        completedQuizCountDailyFourteenDayStreakCount: 0,
        lastDailyLearnerReward: null,
        lastWeeklyStreakReward: null,
        league: 'Bronzana',
        title: 'Početnik',
        isNotificationOn: true,
        isPremiumUser: false,
        isSoundOn: false,
      );

      await firestore
          .collection('users')
          .doc(cred.user!.uid)
          .set(user.toJson());

      // Generate unique username immediately after user creation
      await generateUniqueUsername(cred.user!.uid, name);

      Future.delayed(
        const Duration(seconds: 2),
        () {
          isAuthUpdating.value = false;
        },
      );
    } on FirebaseAuthException catch (error) {
      isAuthUpdating.value = false;
      switch (error.code) {
        case "invalid-email":
          errorMessage = "Vaša email adresa izgleda netačno";
          break;
        case "wrong-password":
          errorMessage = "Vaša lozinka je pogrešna";
          break;
        case "email-already-in-use":
          errorMessage = "Nalog već postoji za unetu email adresu";
          break;
        case "too-many-requests":
          errorMessage = "Previše zahteva";
          break;
        case "network-request-failed":
          //Thrown if there is no internet connection.
          errorMessage = "Nema internet veze";
          break;

        default:
          errorMessage = "Neuspelo kreiranje naloga";
      }
      getErrorSnackBar(
        errorMessage,
      );
    } finally {
      isAuthUpdating.value = false;
    }
  }

  void login(
    email,
    password,
  ) async {
    isAuthUpdating.value = true;
    String errorMessage = '';
    try {
      isLoging = true;
      await _auth.signInWithEmailAndPassword(email: email, password: password);
      // 👇 Don’t set isAuthUpdating false here, let loginRedirect handle it
    } on FirebaseAuthException catch (e) {
      isAuthUpdating.value = false; // keep this for failure
      switch (e.code) {
        case "invalid-email":
          errorMessage = "Vaša email adresa izgleda netačno";
          break;
        case "invalid-credential":
          errorMessage = "Korisnik nije pronađen";
          break;
        case "network-request-failed":
          errorMessage = "Nema internet veze";
          break;
        case "user-disabled":
          errorMessage = "Korisnik je trenutno onemogućen";
          break;
        case "user-not-found":
          errorMessage = "Korisnik nije pronađen";
          break;
        case "wrong-password":
          errorMessage = "Potvrda lozinke netačna";
          break;
        default:
          errorMessage = "Neuspela prijava!";
          break;
      }
      getErrorSnackBar(errorMessage);
    }
  }

  void googleLogin() async {
    final HeartController heartController = Get.find();
    isGoogleAuthUpdating.value = true;
    isLoging = true;
    String errorMessage = '';
    final GoogleSignIn googleSignIn = GoogleSignIn(
      scopes: [
        'email',
        'https://www.googleapis.com/auth/userinfo.profile',
      ],
    );

    try {
      final GoogleSignInAccount? googleSignInAccount =
          await googleSignIn.signIn();

      // Check if user cancelled login
      if (googleSignInAccount == null) {
        // User cancelled login, so exit method
        return;
      }

      // Continue with authentication if user didn't cancel
      final GoogleSignInAuthentication googleAuth =
          await googleSignInAccount.authentication;
      final credentials = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final UserCredential userCredential =
          await _auth.signInWithCredential(credentials);

      User? registereduser = userCredential.user;
      List<String> imgList = [
        'assets/images/img1.png',
        'assets/images/img2.png',
        'assets/images/img3.png',
        'assets/images/img4.png',
      ];
      Random random = Random();
      String randomImage = imgList[random.nextInt(imgList.length)];

      UserModel user = UserModel(
        profilePhoto: randomImage,
        email: userCredential.user!.email,
        name: userCredential.user!.displayName ?? '',
        uid: userCredential.user!.uid,
        neurons: 0,
        lastListResetTimestamp: Timestamp.now(),
        lastStreakUpdate: Timestamp.now(),
        lastActiveTime: Timestamp.now(),
        weeklyStreak: 0,
        hearts: heartController.defaultsHearts.value,
        listOfFavCategories: [],
        listOfLibraryCategories: [],
        achievements: [],
        appOpenCount: 0,
        completedArticleIds: [],
        hasSeenOnboadingScreen: false,
        hasSeenInfoSheet: false,
        consecutiveStreak: 1,
        completedQuizIds: [],
        consecutivePerfectQuizzes: 0,
        correctQuizAnswersCounter: 0,
        hasReachedGoldLeague: false,
        hasReachedSilverLeague: false,
        perfectQuizStreak: 0,
        completedQuizCountDailyFourteenDayStreakCount: 0,
        lastDailyLearnerReward: null,
        lastWeeklyStreakReward: null,
        league: 'Bronzana',
        title: 'Početnik',
        isNotificationOn: true,
        isPremiumUser: false,
        isSoundOn: false,
      );
      final userDocRef = firestore.collection('users').doc(registereduser!.uid);
      if (!(await userDocRef.get()).exists) {
        await firestore
            .collection('users')
            .doc(registereduser.uid)
            .set(user.toJson());
        await generateUniqueUsername(
          registereduser.uid,
          userCredential.user!.displayName ?? '',
        );
      }
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case "account-exists-with-different-credential":
          errorMessage =
              "Nalog već postoji sa istom email adresom ali različitim pristupnim podacima.";
          break;
        case "invalid-email":
          errorMessage = "Nevažeća email adresa";
          break;
        case "network-request-failed":
          errorMessage = "Nema internet veze";
          break;
        case "email-already-in-use":
          errorMessage = "Nalog već postoji za unetu email adresu.";
          break;

        case "user-disabled":
          errorMessage = "Korisnik je trenutno onemogućen";
          break;

        case "user-not-found":
          errorMessage = "Korisnik nije pronađen";
          break;

        case "wrong-password":
          errorMessage = "Pogrešna lozinka";
          break;

        default:
          errorMessage = "Neuspela prijava!";
          break;
      }

      // Display error message only if user didn't cancel login
      getErrorSnackBar(errorMessage);
    } finally {
      isGoogleAuthUpdating.value = false;
    }
  }

  void appleLogin() async {
    final HeartController heartController = Get.find();
    isAppleAuthUpdating.value = true;
    isLoging = true;
    String errorMessage = '';
    final appleProvider = AppleAuthProvider();

    isLoging = true;

    try {
      final UserCredential userCredential =
          await _auth.signInWithProvider(appleProvider);

      try {
        User? registereduser = userCredential.user;
        List<String> imgList = [
          'assets/images/img1.png',
          'assets/images/img2.png',
          'assets/images/img3.png',
          'assets/images/img4.png',
        ];
        Random random = Random();
        String randomImage = imgList[random.nextInt(imgList.length)];

        UserModel user = UserModel(
            profilePhoto: randomImage,
            email: userCredential.user!.email,
            uid: userCredential.user!.uid,
            neurons: 0,
            lastListResetTimestamp: Timestamp.now(),
            lastStreakUpdate: Timestamp.now(),
            lastActiveTime: Timestamp.now(),
            weeklyStreak: 0,
            hearts: heartController.defaultsHearts.value,
            listOfFavCategories: [],
            listOfLibraryCategories: [],
            achievements: [],
            appOpenCount: 0,
            completedArticleIds: [],
            hasSeenOnboadingScreen: false,
            hasSeenInfoSheet: false,
            consecutiveStreak: 1,
            completedQuizIds: [],
            consecutivePerfectQuizzes: 0,
            correctQuizAnswersCounter: 0,
            hasReachedGoldLeague: false,
            hasReachedSilverLeague: false,
                perfectQuizStreak: 0,
            completedQuizCountDailyFourteenDayStreakCount: 0,
            lastDailyLearnerReward: null,
            lastWeeklyStreakReward: null,
            league: 'Bronzana',
            title: 'Početnik',
            isNotificationOn: true,
            isPremiumUser: false,
            isSoundOn: false,
            name: userCredential.user!.displayName ?? '');
        final userDocRef =
            firestore.collection('users').doc(registereduser!.uid);
        if (!(await userDocRef.get()).exists) {
          await firestore
              .collection('users')
              .doc(registereduser.uid)
              .set(user.toJson());

          await generateUniqueUsername(
              registereduser.uid, userCredential.user!.displayName ?? '');
        }
      } on FirebaseAuthException catch (e) {
        switch (e.code) {
          case "account-exists-with-different-credential":
            errorMessage =
                "Nalog već postoji sa istom email adresom ali različitim pristupnim podacima.";
            break;
          case "invalid-email":
            errorMessage = "Nevažeća email adresa";
            break;
          case "network-request-failed":
            errorMessage = "Nema internet veze";
            break;
          case "email-already-in-use":
            errorMessage = "Nalog već postoji za unetu email adresu.";
            break;

          case "user-disabled":
            errorMessage = "Korisnik je trenutno onemogućen";
            break;

          case "user-not-found":
            errorMessage = "Korisnik nije pronađen";
            break;

          case "wrong-password":
            errorMessage = "Pogrešna lozinka";
            break;

          default:
            errorMessage = "Neuspela prijava!";
            break;
        }
        getErrorSnackBar(
          errorMessage,
        );
      }
    } catch (e) {
      return null;
    } finally {
      isAppleAuthUpdating.value = false;
    }
  }

  void forgorPassword(email) async {
    String errorMessage = '';
    try {
      await _auth.sendPasswordResetEmail(
        email: email,
      );
      if (Navigator.canPop(Get.context!)) {
        Get.back();
      }
      getSuccessSnackBar('Email za resetovanje lozinke je poslat!');
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case "invalid-email":
          errorMessage = "Nevažeća email adresa";
          break;
        case "network-request-failed":
          errorMessage = "Nema internet veze";
          break;

        case "user-disabled":
          errorMessage = "Korisnik je trenutno onemogućen";
          break;

        case "user-not-found":
          errorMessage = "Korisnik nije pronađen";
          break;

        default:
          errorMessage = 'Nešto je pošlo po zlu, Molimo pokušajte ponovo';
          break;
      }
      getErrorSnackBar(
        errorMessage,
      );
    }
  }

  void signOut() async {
    try {
      isSigningOut.value = true;

      // Clear user token and Firestore data
      try {
        final userId = FirebaseAuth.instance.currentUser?.uid;
        if (userId != null) {
          await FirebaseMessaging.instance.deleteToken();
          await FirebaseFirestore.instance
              .collection('users')
              .doc(userId)
              .update({
            'deviceToken': null,
          });
        }
      } catch (e) {
        debugPrint('Error clearing user tokens: $e');
      }

      // Clear all controller data BEFORE signing out
      await _performComprehensiveCleanup();

// Sign out from Google
      await _performGoogleSignOut();

      // Sign out from Firebase Auth
      await _auth.signOut();
    } catch (e) {
      debugPrint('Error during sign out: $e');
    } finally {
      isSigningOut.value = false;
    }
  }

  /// Perform comprehensive cleanup of all app data
  Future<void> _performComprehensiveCleanup() async {
    try {
      // Delete ProfileController completely - it will be recreated on next login
      try {
        Get.delete<ProfileController>();
        debugPrint('🗑️ ProfileController deleted on logout');
      } catch (e) {
        debugPrint('ProfileController not found during cleanup');
      }

      // Clear image caches in background (non-blocking)
      EnhancedCacheManager.clearCache(onlyExpired: false).catchError((e) {
        debugPrint('Error clearing cache: $e');
      });

      debugPrint('🎯 Comprehensive cleanup completed - controllers preserved');
    } catch (e) {
      debugPrint('Error during comprehensive cleanup: $e');
    }
  }

  /// Handle Google sign out properly
  Future<void> _performGoogleSignOut() async {
    try {
      final GoogleSignIn googleSignIn = GoogleSignIn(
        scopes: [
          'email',
          'https://www.googleapis.com/auth/userinfo.profile',
          'https://www.googleapis.com/auth/userinfo.email',
        ],
      );

      // Sign out and disconnect Google account
      await googleSignIn.signOut();
      await googleSignIn.disconnect();
    } catch (e) {
      debugPrint('Error during Google sign out: $e');
    }
  }
}
