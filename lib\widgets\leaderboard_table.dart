import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/league_user_model.dart';
import '../controllers/profile_controller.dart';
import '../res/style.dart';
import 'leaderboard_widgets.dart';

class LeaderboardTable extends StatelessWidget {
  final List<LeaguePlayerModel> players;
  final ProfileController profileController;

  const LeaderboardTable({
    super.key,
    required this.players,
    required this.profileController,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const RankHeaderWidget(isOnResult: false),
        const SizedBox(height: 16),
        const Text(
          'Tabela',
          style: TextStyle(
            fontSize: 18.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8.0),
        _buildDataTable(),
        Container(height: 150, color: Colors.transparent),
      ],
    );
  }

  Widget _buildDataTable() {
    return Container(
      width: double.infinity,
      decoration: _tableDecoration(),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: DataTable(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade200, width: 1),
            borderRadius: BorderRadius.circular(12),
          ),
          headingRowColor: WidgetStateProperty.all(Colors.grey.shade50),
          dividerThickness: 0,
          horizontalMargin: 20,
          columnSpacing: 40,
          headingRowHeight: 50,
          dataRowMinHeight: 56,
          dataRowMaxHeight: 56,
          columns: _buildColumns(),
          rows: _buildRows(),
        ),
      ),
    );
  }

  BoxDecoration _tableDecoration() {
    return BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      border: Border.all(color: Colors.grey.shade200, width: 1),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withValues(alpha: 0.08),
          spreadRadius: 0,
          blurRadius: 10,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  List<DataColumn> _buildColumns() {
    final headerStyle = TextStyle(
      fontWeight: FontWeight.w600,
      color: Colors.grey.shade700,
      fontSize: 14,
    );

    return [
      DataColumn(label: Text('Rank', style: headerStyle)),
      DataColumn(label: Text('Ime', style: headerStyle)),
      DataColumn(label: Text('Neuroni', style: headerStyle)),
    ];
  }

  List<DataRow> _buildRows() {
    final currentUid = profileController.userr.value.uid ?? '';
    final currentName = profileController.userr.value.uniqueName ?? '';

    return players.asMap().entries.map((entry) {
      final index = entry.key;
      final player = entry.value;
      final isCurrentUser = player.playerId == currentUid;

      return DataRow(
        color: WidgetStateProperty.all(_getRowColor(index, isCurrentUser)),
        cells: [
          _buildRankCell(index, isCurrentUser),
          _buildNameCell(player, isCurrentUser, currentName),
          _buildScoreCell(player, isCurrentUser),
        ],
      );
    }).toList();
  }

  Color _getRowColor(int index, bool isCurrentUser) {
    if (isCurrentUser) {
      return const Color(0xff7CE099).withValues(alpha: 0.08);
    }
    return index % 2 == 0 ? Colors.white : Colors.grey.shade50;
  }

  Color _getTextColor(bool isCurrentUser) {
    return isCurrentUser ? const Color(0xff7CE099) : Colors.grey.shade800;
  }

  FontWeight _getFontWeight(bool isCurrentUser, {bool isBold = false}) {
    if (isCurrentUser) {
      return FontWeight.w600;
    }
    return isBold ? FontWeight.w500 : FontWeight.w400;
  }

  DataCell _buildRankCell(int index, bool isCurrentUser) {
    return DataCell(
      Text(
        '#${index + 1}',
        style: TextStyle(
          color: _getTextColor(isCurrentUser),
          fontWeight: _getFontWeight(isCurrentUser, isBold: true),
          fontSize: 14,
        ),
      ),
    );
  }

  DataCell _buildNameCell(LeaguePlayerModel player, bool isCurrentUser, String currentName) {
    final displayName = isCurrentUser ? currentName : (player.username ?? 'N/A');
    
    return DataCell(
      Text(
        displayName,
        style: TextStyle(
          color: _getTextColor(isCurrentUser),
          fontWeight: _getFontWeight(isCurrentUser),
          fontSize: 14,
        ),
      ),
    );
  }

  DataCell _buildScoreCell(LeaguePlayerModel player, bool isCurrentUser) {
    return DataCell(
      Text(
        player.weeklyScore?.toString() ?? '0',
        style: TextStyle(
          color: _getTextColor(isCurrentUser),
          fontWeight: _getFontWeight(isCurrentUser, isBold: true),
          fontSize: 14,
        ),
      ),
    );
  }
}