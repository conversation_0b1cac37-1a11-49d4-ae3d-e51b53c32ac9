
Leaderboards and Leagues

- 20 Leagues System:

  - Users are grouped into leagues based on their points.
  - The top league is the highest level, and the lowest is for beginners.
  - Regular promotion and demotion based on performance to keep competition active.
  - Users that are going in the league are up our top 3, and the last three are regulated (going down or staying in the same if it’s a beginner league at the begininig)
  - Leagues last for one week.

- Weekly Competition: Leagues run for one week, starting on Monday and ending on Sunday.
- Placement: You're placed in a league with max 10 other users based on their weeklyScore (expeerience points) from the previous week. 
- If the user doesn’t show up in the app for 30 days, neurons go back to 0

  
League Names and Durations: 

1. Bronze League (1 week) - Bronzana Liga (1 nedelja)
2. Silver League (1 week) - Srebrna Liga (1 nedelja)
3. Gold League (1 week) - Zlatna Liga (1 nedelja)
4. Platinum League (1 week) - Platinum Liga (1 nedelja)
5. Diamond League (1 week) - Dijamantska Liga (1 nedelja)
6. Ruby League (1 week) - Rubin Liga (1 nedelja)
7. Sapphire League (1 week) - Safirna Liga (1 nedelja)
8. Emerald League (1 week) - Smaragdna Liga (1 nedelja)
9. Topaz League (1 week) - Topaz Liga (1 nedelja)
10. Amethyst League (1 week) - Ametist Liga (1 nedelja)
11. Opal League (1 week) - Opal Liga (1 nedelja)
12. Quartz League (1 week) - Kvarc Liga (1 nedelja)
13. Jade League (1 week) - Žad Liga (1 nedelja)
14. Crystal League (1 week) - Kristalna Liga (1 nedelja)
15. Fenix League (1 week) - Fenix Liga (1 nedelja)
16. Onyx League (1 week) - Oniks Liga (1 nedelja)
17. Galaxy League (1 week) - Galakisjksa Liga (1 nedelja)
18. Coral League (1 week) - Koralska Liga (1 nedelja)
19. Jupiter League (1 week) - Jupiter Liga (1 nedelja)
20. Elite League (1 week) - Elitna Liga (1 nedelja)

___________________________________________________________________________
Case for Hamza (Developer)

 1. League Grouping
●	Description: Users are grouped into different leagues based on their weeklyScore points earned from the previous week.
●	Scenario:
○	On Monday at 00:01, the system reviews the total weeklyScore points accumulated by users during the previous week (Monday 00:00 to Sunday 23:59).
○	Based on their total weeklyScore, users are assigned to a league. For example:
■	Beginners start in the Bronzana Liga.
■	Users with higher weeklyScore from the previous week are promoted to leagues like Srebrna Liga, Zlatna Liga, etc.
■	The top three in each league are promoted to the next league tier.
■	The last three users are demoted to the lower league, unless they are already in the Bronzana Liga.
2. Promotion and Demotion
●	Description: Regular promotion and demotion to maintain a competitive dynamic.
●	Scenario:
○	At the end of each league week (Sunday 23:59), the system evaluates the user rankings.
○	The top 3 users from each league are promoted to the next league up.
■	For example, users finishing in the top 3 in Srebrna Liga are promoted to Zlatna Liga.
○	The bottom 3 users are demoted to the lower league unless they are already in Bronzana Liga.
■	If users are already in Bronzana Liga and are among the bottom 3, they remain in the Bronzana Liga.
○	If the user is inactive (no weeklyScore earned), they will automatically be considered among the bottom 3 for demotion.
3. Weekly Competition
●	Description: Leagues last one week, from Monday 00:00 to Sunday 23:59.
●	Scenario:
○	A new league competition starts every Monday at 00:00.
○	During the week, users earn weeklyScore by completing app activities (e.g., answering quiz questions, completing tasks).
○	weeklyScore is accumulated throughout the week and updated in real-time on the leaderboard for the league.
○	At the end of the week, Sunday 23:59, the league standings are finalized based on the total weeklyScore points.
○	Once standings are finalized, promotions and demotions are calculated as described above.
4. weeklyScore Calculation
●	Description: weeklyScore determines league placement and movement.
●	Scenario:
○	Users gain weeklyScore from various activities in the app, such as answering quiz questions or participating in educational tasks.
○	weeklyScore is tallied during the week and is reset to zero at the beginning of the new week for the purpose of weekly competition.
○	Historical Neurons is still stored to track long-term progress, but weekly weeklyScore only affects current league status.
5. League Names and Tiers
●	Description: Leagues have distinct Serbian names and represent different tiers of competition.
●	Scenario:
○	Upon league assignment, users will see the name of the league they are in for the week. For example:
■	Beginners will see "Dobrodošli u Bronzanu Ligu!" when they enter the Bronzana Liga.
■	Users promoted to higher leagues will receive a congratulatory message like "Čestitamo, prešli ste u Srebrnu Ligu!".
○	The league name will be displayed prominently on their dashboard along with their current rank.
○	The leagues, ordered from lowest to highest, are:
■	Bronzana Liga
■	Srebrna Liga
■	Zlatna Liga
■	Platinum Liga
■	Dijamantska Liga
■	Rubin Liga
■	Safirna Liga
■	Smaragdna Liga
■	Topaz Liga
■	Ametist Liga
■	Opal Liga
■	Kvarc Liga
■	Žad Liga
■	Kristalna Liga
■	Fenix Liga
■	Oniks Liga
■	Galaktička Liga
■	Koralska Liga
■	Jupiter Liga
■	Elitna Liga
6. League Transitions
●	Description: Users are notified of their league transitions (promotion, demotion, or staying).
●	Scenario:
○	On Monday at 00:00, when the new week begins:
■	Users who were promoted will receive a notification: "Čestitamo! Promovisani ste u [next league name]."
■	Users who were demoted will receive a notification: "Nažalost, vraćeni ste u [previous league name]."
■	Users who stayed in the same league will receive a notification: "Ostali ste u [current league name] za ovu nedelju."
7. Edge Cases
●	Inactive Users: Users who do not earn any weeklyScore during the week are automatically considered for demotion if not in the beginner league.
●	Tie Breakers: In the case of a tie in weeklyScore (e.g., two users with the same score):
○	The user who reached the score earlier in the week takes precedence for promotion.
○	If tied users are in the demotion zone, the same rule applies but in reverse; the last user to reach the score is demoted.




END OF CASE
________________________________________________________________________

