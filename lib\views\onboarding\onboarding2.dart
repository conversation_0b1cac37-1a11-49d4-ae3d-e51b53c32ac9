import 'package:bibl/bnb.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:get/get.dart';

import '../../services/notification_service.dart';
import '../../widgets/customappbar.dart';

class Onboarding2 extends StatefulWidget {
  const Onboarding2({super.key});

  @override
  State<Onboarding2> createState() => _Onboarding1Stat2State();
}

class _Onboarding1Stat2State extends State<Onboarding2> {
  bool isAllowNotificationProcessing = false;
  bool isContinueProcessing = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        widget: null,
        title: 'umniLab notifikacije',
      ),
      body: Safe<PERSON>rea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Container(
                height: 300,
                decoration: BoxDecoration(
                    border: Border.all(
                        color: const Color(0xff9610FF).withValues(alpha: 0.5)),
                    color: const Color(0xffF8EFFF),
                    borderRadius: BorderRadius.circular(10)),
                child: const Padding(
                  padding: EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      CircleAvatar(
                          radius: 70,
                          backgroundImage:
                              AssetImage('assets/images/onBoarding2.webp')),
                      Spacer(),
                      Txt(
                        txt:
                            '"Znanje je svetlost koje osvetljava naš put kroz život"',
                        fontSize: 16,
                        maxLines: 5,
                        textAlign: TextAlign.center,
                        fontWeight: FontWeight.normal,
                        fontColor: Colors.black,
                      ),
                      SizedBox(
                        height: 8,
                      ),
                      Txt(
                        txt: '- Mihajlo Pupin',
                        fontSize: 16,
                        textAlign: TextAlign.center,
                        fontColor: Colors.black,
                      ),
                      Spacer(
                        flex: 2,
                      ),
                    ],
                  ),
                ),
              ),
              const Spacer(),
              buttonContainer(
                onTap: () async {
                  try {
                    setState(() {
                      isAllowNotificationProcessing = true;
                    });
                    // Request notification permission
                    final userId = FirebaseAuth.instance.currentUser?.uid;

                    if (userId != null) {
                      // Initialize notification service first
                      final notificationService = NotificationService();
                      
                      // Request both FCM and local notification permissions
                      FirebaseMessaging messaging = FirebaseMessaging.instance;
                      NotificationSettings fcmSettings =
                          await messaging.requestPermission(
                        alert: true,
                        badge: true,
                        sound: true,
                      );

                      // Initialize notifications (this also requests local permissions)
                      bool localPermissionGranted = await notificationService.initNotification();
                      
                      bool isFCMGranted = fcmSettings.authorizationStatus ==
                          AuthorizationStatus.authorized;
                      
                      // Only enable notifications if both FCM and local permissions are granted
                      bool allPermissionsGranted = isFCMGranted && localPermissionGranted;
                      
                      await FirebaseFirestore.instance
                          .collection('users')
                          .doc(userId)
                          .update({
                        'hasSeenOnboadingScreen': true,
                        'isNotificationOn': allPermissionsGranted,
                      });
                    }

                    // Navigate to the main screen with smoother transition
                    Get.offAll(() => const BNB(),
                        transition: Transition.fadeIn,
                        duration: const Duration(milliseconds: 400),
                        curve: Curves.easeInOut);
                    setState(() {
                      isAllowNotificationProcessing = false;
                    });
                  } catch (e) {
                    // Navigate to the main screen with smoother transition
                    Get.offAll(() => const BNB(),
                        transition: Transition.fadeIn,
                        duration: const Duration(milliseconds: 400),
                        curve: Curves.easeInOut);
                    setState(() {
                      isAllowNotificationProcessing = false;
                    });
                    //
                  }
                },
                child: isAllowNotificationProcessing
                    ? const Center(
                        child: CircularProgressIndicator(
                        color: Colors.white,
                      ))
                    : const Txt(
                        txt: 'Dozvoli Notifikacije',
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        fontColor: Colors.white,
                      ),
              ),
              const SizedBox(height: 16),
              outlineButtonContainer(
                onTap: () async {
                  try {
                    setState(() {
                      isContinueProcessing = true;
                    });

                    final userId = FirebaseAuth.instance.currentUser?.uid;
                    if (userId != null) {
                      // Default isNotificationOn to false
                      await FirebaseFirestore.instance
                          .collection('users')
                          .doc(userId)
                          .update({
                        'hasSeenOnboadingScreen': true,
                        'isNotificationOn': false,
                      });
                    }

                    // Navigate to the main screen with smoother transition
                    Get.offAll(() => const BNB(),
                        transition: Transition.fadeIn,
                        duration: const Duration(milliseconds: 400),
                        curve: Curves.easeInOut);

                    setState(() {
                      isContinueProcessing = false;
                    });
                  } catch (e) {
                    Get.offAll(() => const BNB(),
                        transition: Transition.fadeIn,
                        duration: const Duration(milliseconds: 400),
                        curve: Curves.easeInOut);

                    setState(() {
                      isContinueProcessing = false;
                    });
                  }
                },
                child: isContinueProcessing
                    ? const Center(
                        child: CircularProgressIndicator(
                        color: mainColor,
                      ))
                    : const Center(
                        child: Txt(
                          txt: 'Nastavi',
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontColor: mainColor,
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
