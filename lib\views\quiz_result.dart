// ignore_for_file: empty_catches
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:bibl/res/style.dart';
import 'package:get/get.dart';
import '../controllers/analytics_controller.dart';
import '../models/question_model.dart';
import '../models/quiz_model.dart';
import '../services/reward_service.dart';
import '../services/lessonquiz_completion_service.dart';
import '../widgets/customappbar.dart';
import '../widgets/leaderboard_widgets.dart';
import 'completion.dart';

class QuizResult extends StatefulWidget {
  final QuizModel? finishedQuizModel;
  final ShuffleQuizModel? finishedShuffleQuizModel;
  final List<String?>? selectedAnswers;
  final List<bool?>? selectedAnswersOfShuffleQuiz;

  const QuizResult({
    super.key,
    this.finishedQuizModel,
    this.selectedAnswers,
    this.finishedShuffleQuizModel,
    this.selectedAnswersOfShuffleQuiz,
  });

  @override
  State<QuizResult> createState() => _QuizResultState();
}

class _QuizResultState extends State<QuizResult> {
  ProfileController profileController = Get.find();
  final AnalticsController analticsController = AnalticsController();
  final Map<String, String> categoryFieldMap = {
    'Mitologija': 'completedQuizesOfMythologyCategory',
    'Umetnost': 'completedQuizesOfArtCategory',
    'Nauka': 'completedQuizesOfScienceCategory',
    'Kosmos': 'completedQuizesOfCosmosCategory',
    'Istorija': 'completedQuizesOfHistoryCategory',
  };
  int totalEarnedPoints = 0;
  bool isQuizCompletedWithinThirtyDays = false;

  @override
  void initState() {
    super.initState();

    if (profileController.userr.value.uid != null) {
      final quizId = widget.finishedQuizModel?.quizId ??
          widget.finishedShuffleQuizModel?.quizId;
      final category = widget.finishedQuizModel?.category ??
          widget.finishedShuffleQuizModel?.category;

      // Check if this quiz was completed within the last 30 days
      if (quizId != null) {
        final completionService = LessonQuizCompletionService();
        isQuizCompletedWithinThirtyDays =
            completionService.isCompletedWithinThirtyDays(
                quizId,
                profileController
                    .userr.value.completedLessonQuizesInThirtyDays);
      }

      // Only call reward functions if quiz wasn't completed within 30 days
      if (!isQuizCompletedWithinThirtyDays) {
        if (quizId != null && category != null) {
          onQuizCompletion(
              profileController.userr.value.uid!, quizId, category);
        }
        rewardUserOptimized(profileController.userr.value.uid!, quizId!);
        trackDailyQuizCompletion();
      }

      calculateNeuronsPoints();

      // Defer dialog-related operations until after build is complete
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        if (!isQuizCompletedWithinThirtyDays) {
          await handleQuizRewards();
        }
      });
    }
  }

  void calculateNeuronsPoints() {
    int calculatePoints(List? questions, List? answers, int pointsPerQuestion) {
      if (questions == null || answers == null) return 0;

      int correctAnswers = 0;
      for (int i = 0; i < questions.length; i++) {
        if (answers[i] == questions[i]?.correctOption) {
          correctAnswers++;
        }
      }
      return correctAnswers * pointsPerQuestion;
    }

    if (widget.finishedQuizModel != null && widget.selectedAnswers != null) {
      totalEarnedPoints = calculatePoints(
          widget.finishedQuizModel?.questionsList, widget.selectedAnswers, 10);
    } else if (widget.finishedShuffleQuizModel != null &&
        widget.selectedAnswersOfShuffleQuiz != null) {
      totalEarnedPoints = calculatePoints(
          widget.finishedShuffleQuizModel?.questionsList,
          widget.selectedAnswersOfShuffleQuiz,
          5);
    }
  }

  Future<void> trackDailyQuizCompletion() async {
    final now = Timestamp.now();
    final currentDate = now.toDate();
    final lastQuizCompletion =
        profileController.userr.value.lastDailyQuizCompletion?.toDate();

    // Check if user completed a quiz today already
    if (lastQuizCompletion != null &&
        lastQuizCompletion.year == currentDate.year &&
        lastQuizCompletion.month == currentDate.month &&
        lastQuizCompletion.day == currentDate.day) {
      return; // Already completed a quiz today
    }

    // Calculate streak for consistent learner achievement
    int streakCount = 1;
    if (lastQuizCompletion != null) {
      final daysDifference = currentDate.difference(lastQuizCompletion).inDays;
      if (daysDifference == 1) {
        // Consecutive day
        streakCount = (profileController.userr.value
                    .completedQuizCountDailyFourteenDayStreakCount ??
                0) +
            1;
      } else if (daysDifference > 1) {
        // Streak broken, reset to 1
        streakCount = 1;
      }
    }

    final updateData = {
      'lastDailyQuizCompletion': now,
      'completedQuizCountDailyFourteenDayStreakCount': streakCount
    };

    if (streakCount >= 14) {
      await Future.wait([
        rewardUserForConsistentLearning(profileController.userr.value.uid!),
        firestore
            .collection('users')
            .doc(profileController.userr.value.uid!)
            .update(updateData),
      ]);
    } else {
      await firestore
          .collection('users')
          .doc(profileController.userr.value.uid!)
          .update(updateData);
    }

    // Update local values
    profileController.userr.value.lastDailyQuizCompletion = now;
    profileController.userr.value
        .completedQuizCountDailyFourteenDayStreakCount = streakCount;
  }

  Future<void> rewardUserForConsistentLearning(String userID) async {
    final rewardService = RewardService(userID);
    await rewardService.consistentLearner();
  }

  Future<List<String>> fetchAllQuizIDs() async {
    final quizIDs = <String>[];
    final quizCollections = ['quizes', 'shuffleQuizes'];
    for (var collection in quizCollections) {
      final snapshot = await firestore.collection(collection).get();
      quizIDs.addAll(snapshot.docs.map((doc) => doc.id));
    }
    return quizIDs;
  }

  Future<void> updateQuizCompletion(String userID, String quizID) async {
    final userDoc = firestore.collection('users').doc(userID);
    await userDoc.update({
      'listOfMultiCategoryQuizes': FieldValue.arrayUnion([quizID])
    });
  }

  Future<bool> checkEligibilityOptimized() async {
    try {
      List<String> userQuizzes =
          profileController.userr.value.listOfMultiCategoryQuizes ?? [];
      List<String> allQuizzes = await fetchAllQuizIDs();
      return allQuizzes.every((quizID) => userQuizzes.contains(quizID));
    } catch (e) {
      return false;
    }
  }

  Future<void> rewardUserOptimized(String userID, String quizID) async {
    try {
      final user = profileController.userr.value;
      if (user.listOfMultiCategoryQuizes == null ||
          !user.listOfMultiCategoryQuizes!.contains(quizID)) {
        await updateQuizCompletion(userID, quizID);
      }

      final now = Timestamp.now();
      final lastRewarded = user.multiCategoryLastRewardedAt ?? Timestamp(0, 0);
      if (now.seconds - lastRewarded.seconds > 24 * 60 * 60) {
        final isEligible = await checkEligibilityOptimized();
        if (isEligible) {
          final rewardService = RewardService(user.uid!);
          await Future.wait([
            rewardService.multiCategoryMaster(),
            firestore.collection('users').doc(userID).update({
              'multiCategoryLastRewardedAt': now,
              'listOfMultiCategoryQuizes': [],
            }),
          ]);
          user.multiCategoryLastRewardedAt = now;
          user.listOfMultiCategoryQuizes = [];
        }
      }
    } catch (e) {
      //
    }
  }

  Future<void> onQuizCompletion(
      String userId, String quizId, String category) async {
    await updateCompletedQuizzes(userId, quizId, category);
    await checkAndRewardAchievement(userId, category);
  }

  Future<void> updateCompletedQuizzes(
      String userId, String quizId, String category) async {
    final field = categoryFieldMap[category];
    if (field != null) {
      try {
        await firestore.runTransaction((transaction) async {
          final userDocRef = firestore.collection('users').doc(userId);
          final userDocSnapshot = await transaction.get(userDocRef);
          if (!userDocSnapshot.exists) {
            throw Exception('User does not exist');
          }
          transaction.update(userDocRef, {
            field: FieldValue.arrayUnion([quizId])
          });
        });
      } catch (e) {
        //
      }
    }
  }

  Future<void> checkAndRewardAchievement(String userId, String category) async {
    try {
      final rewardService = RewardService(profileController.userr.value.uid!);
      final field = categoryFieldMap[category];
      if (field != null) {
        final allQuizzes = await fetchQuizzesByCategory(category);
        final completedQuizzes =
            await firestore.collection('users').doc(userId).get();
        bool allCompleted = allQuizzes
            .every((quizId) => completedQuizzes[field].contains(quizId));
        if (allCompleted) {
          rewardForCategory(category, rewardService);
        }
      }
    } catch (e) {
      //
    }
  }

  Future<List<String>> fetchQuizzesByCategory(String category) async {
    try {
      final querySnapshot = await firestore
          .collection('quizzes')
          .where('category', isEqualTo: category)
          .get();
      return querySnapshot.docs.map((doc) => doc.id).toList();
    } catch (e) {
      //
      return [];
    }
  }

  void rewardForCategory(String category, RewardService rewardService) {
    final achievements = profileController.userr.value.achievements ?? [];
    switch (category) {
      case 'Mythology':
        if (!achievements.contains('mythology_master')) {
          rewardService.mythologyMaster();
        }
        break;
      case 'Art':
        if (!achievements.contains('art_aficionado')) {
          rewardService.artAficionado();
        }
        break;
      case 'Science':
        if (!achievements.contains('science_sage')) {
          rewardService.scienceSage();
        }
        break;
      case 'Cosmos':
        if (!achievements.contains('cosmos_conqueror')) {
          rewardService.cosmosConqueror();
        }
        break;
      case 'History':
        if (!achievements.contains('history_buff')) {
          rewardService.historyBuff();
        }
        break;
      default:
        break;
    }
  }

  Future<void> updateCorrectAnswerCount() async {
    RewardService rewardService =
        RewardService(profileController.userr.value.uid!);
    int correctAnswers = 0;
    int totalQuestions = 0;

    if (widget.finishedQuizModel != null && widget.selectedAnswers != null) {
      totalQuestions = widget.finishedQuizModel?.questionsList?.length ?? 0;
      for (int i = 0; i < totalQuestions; i++) {
        final question = widget.finishedQuizModel?.questionsList?[i];
        final userAnswer = widget.selectedAnswers![i];
        if (userAnswer == question?.correctOption) {
          correctAnswers++;
        }
      }
    } else if (widget.finishedShuffleQuizModel != null &&
        widget.selectedAnswersOfShuffleQuiz != null) {
      totalQuestions =
          widget.finishedShuffleQuizModel?.questionsList?.length ?? 0;
      for (int i = 0; i < totalQuestions; i++) {
        final question = widget.finishedShuffleQuizModel?.questionsList?[i];
        final userAnswer = widget.selectedAnswersOfShuffleQuiz![i];
        if (userAnswer == question?.correctOption) {
          correctAnswers++;
        }
      }
    }

    var userRef =
        firestore.collection('users').doc(profileController.userr.value.uid!);
    await userRef.update(
        {'correctQuizAnswersCounter': FieldValue.increment(correctAnswers)});

    // Update local counter and check knowledge seeker achievement
    final newCorrectAnswersTotal =
        (profileController.userr.value.correctQuizAnswersCounter ?? 0) +
            correctAnswers;
    profileController.userr.value.correctQuizAnswersCounter =
        newCorrectAnswersTotal;

    if (newCorrectAnswersTotal >= 100 &&
        !profileController.userr.value.achievements!
            .contains('knowledge_seeker')) {
      rewardService.knowledgeSeeker();
    }

    if (profileController.userr.value.correctQuizAnswersCounter! > 100 &&
        !profileController.userr.value.achievements!
            .contains('knowledge_seeker')) {
      rewardService.knowledgeSeeker();
    }
  }

  double _calculateAccuracy() {
    int correctAnswers = 0;
    int totalQuestions = 0;

    if (widget.finishedQuizModel != null) {
      totalQuestions = widget.finishedQuizModel?.questionsList?.length ?? 0;
      if (widget.selectedAnswers != null) {
        for (int i = 0; i < totalQuestions; i++) {
          final question = widget.finishedQuizModel?.questionsList?[i];
          final userAnswer = widget.selectedAnswers![i];
          if (userAnswer == question?.correctOption) {
            correctAnswers++;
          }
        }
      }
    } else if (widget.finishedShuffleQuizModel != null) {
      totalQuestions =
          widget.finishedShuffleQuizModel?.questionsList?.length ?? 0;
      if (widget.selectedAnswersOfShuffleQuiz != null) {
        for (int i = 0; i < totalQuestions; i++) {
          final question = widget.finishedShuffleQuizModel?.questionsList?[i];
          final userAnswer = widget.selectedAnswersOfShuffleQuiz![i];
          if (userAnswer == question?.correctOption) {
            correctAnswers++;
          }
        }
      }
    }

    return (correctAnswers / totalQuestions) * 100;
  }

  // Removed old updateQuizPerformance method - using perfectQuizStreak tracking in handleQuizRewards instead

  Future<void> handleQuizRewards() async {
    RewardService rewardService =
        RewardService(profileController.userr.value.uid!);
    double accuracy = _calculateAccuracy();

    // Handle accuracy achievements (check for 100% first, then 90%)
    if (accuracy == 100) {
      rewardService.quizWhiz();

      // Update perfect quiz streak for perfectionist achievement
      final currentPerfectStreak =
          profileController.userr.value.perfectQuizStreak ?? 0;
      final newPerfectStreak = currentPerfectStreak + 1;

      await firestore
          .collection('users')
          .doc(profileController.userr.value.uid!)
          .update({
        'perfectQuizStreak': newPerfectStreak,
      });
      profileController.userr.value.perfectQuizStreak = newPerfectStreak;

      // Check perfectionist achievement (5 consecutive perfect quizzes)
      if (newPerfectStreak >= 5 &&
          !profileController.userr.value.achievements!
              .contains('perfectionist')) {
        rewardService.perfectionist();
      }
    } else if (accuracy >= 90) {
      rewardService.accuracyAce();

      // Reset perfect quiz streak
      await firestore
          .collection('users')
          .doc(profileController.userr.value.uid!)
          .update({
        'perfectQuizStreak': 0,
      });
      profileController.userr.value.perfectQuizStreak = 0;
    } else {
      // Reset perfect quiz streak
      await firestore
          .collection('users')
          .doc(profileController.userr.value.uid!)
          .update({
        'perfectQuizStreak': 0,
      });
      profileController.userr.value.perfectQuizStreak = 0;
    }

    // Update quiz count using list-based approach to prevent duplicates
    final quizId = widget.finishedQuizModel?.quizId ??
        widget.finishedShuffleQuizModel?.quizId;

    if (quizId != null) {
      final completedQuizIds =
          profileController.userr.value.completedQuizIds ?? [];

      // Only add if not already completed
      if (!completedQuizIds.contains(quizId)) {
        final updatedQuizIds = [...completedQuizIds, quizId];

        // Update both Firestore and local state
        firestore
            .collection('users')
            .doc(profileController.userr.value.uid!)
            .update({
          'completedQuizIds': FieldValue.arrayUnion([quizId])
        });

        // Update local profile controller
        profileController.userr.value.completedQuizIds = updatedQuizIds;

        // Schedule the UI refresh for after the current build cycle
        WidgetsBinding.instance.addPostFrameCallback((_) {
          profileController.userr.refresh();
        });

        // Handle quiz count achievements with proper checking
        final achievements = profileController.userr.value.achievements ?? [];
        final newQuizCount = updatedQuizIds.length;

        if (newQuizCount >= 1 && !achievements.contains('first_steps')) {
          rewardService.firstSteps();
        }
        if (newQuizCount >= 10 && !achievements.contains('quiz_wiz')) {
          rewardService
              .quizWiz(); // This is the "Quiz Wiz: Complete 10 quizzes" achievement
        }
        if (newQuizCount >= 20 &&
            !achievements.contains('classic_quiz_master')) {
          rewardService.classicQuizMaster();
        }
        if (newQuizCount >= 50 && !achievements.contains('quiz_marathoner')) {
          rewardService.quizMarathoner();
        }
        if (newQuizCount >= 100 && !achievements.contains('quiz_veteran')) {
          rewardService.quizVeteran();
        }
      }
    }
    if (!profileController.userr.value.achievements!
        .contains('first_correct_answer')) {
      bool hasAnyCorrectAnswer = false;

      // Check if ANY question was answered correctly in the quiz
      if (widget.selectedAnswers != null && widget.finishedQuizModel != null) {
        for (int i = 0; i < widget.selectedAnswers!.length; i++) {
          if (widget.selectedAnswers![i] ==
              widget.finishedQuizModel?.questionsList?[i].correctOption) {
            hasAnyCorrectAnswer = true;
            break;
          }
        }
      } else if (widget.selectedAnswersOfShuffleQuiz != null &&
          widget.finishedShuffleQuizModel != null) {
        for (int i = 0; i < widget.selectedAnswersOfShuffleQuiz!.length; i++) {
          if (widget.selectedAnswersOfShuffleQuiz![i] ==
              widget
                  .finishedShuffleQuizModel?.questionsList?[i].correctOption) {
            hasAnyCorrectAnswer = true;
            break;
          }
        }
      }

      if (hasAnyCorrectAnswer) {
        rewardService.firstCorrectAnswer();
      }
    }
    updateCorrectAnswerCount();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: CustomAppBar(title: 'Rezultati', isBackButton: false),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Stack(
            children: [
              Positioned.fill(
                child: ScrollConfiguration(
                  behavior: const ScrollBehavior(),
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      const RankHeaderWidget(
                        isOnResult: true,
                      ),
                      const SizedBox(height: 10),
                      widget.selectedAnswers != null
                          ? buildQuizList(
                              widget.finishedQuizModel, widget.selectedAnswers)
                          : buildShuffleQuizList(
                              widget.finishedShuffleQuizModel,
                              widget.selectedAnswersOfShuffleQuiz),
                      const SizedBox(height: 150),
                    ],
                  ),
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: SafeArea(
                  child: buttonContainer(
                    text: 'Potvrdi',
                    onTap: () {
                      final quizId = widget.finishedQuizModel?.quizId ??
                          widget.finishedShuffleQuizModel?.quizId;
                      Get.off(() => Completion(
                            id: quizId!,
                            isFromLesson: false,
                            earnedNeurons: totalEarnedPoints,
                          ));
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildQuizList(QuizModel? quizModel, List<String?>? selectedAnswers) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      separatorBuilder: (context, index) => const SizedBox(height: 20),
      itemCount: quizModel?.questionsList?.length ?? 0,
      itemBuilder: (context, index) {
        final question = quizModel?.questionsList?[index];
        final userAnswer = selectedAnswers![index];
        final isCorrect = userAnswer == question?.correctOption;
        return
            // GestureDetector(
            // onTap: () {
            //   Get.to(() => AnswerExplanation(
            //       question: question!,
            //       selectedAnswers: selectedAnswers,
            //       quiz: quizModel!));
            // },
            // child:
            buildQuestionContainer(
                question, index, quizModel?.questionsList?.length, isCorrect);
        // );
      },
    );
  }

  Widget buildShuffleQuizList(
      ShuffleQuizModel? shuffleQuizModel, List<bool?>? selectedAnswers) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      separatorBuilder: (context, index) => const SizedBox(height: 20),
      itemCount: shuffleQuizModel?.questionsList?.length ?? 0,
      itemBuilder: (context, index) {
        final question = shuffleQuizModel?.questionsList?[index];
        final userAnswer = selectedAnswers![index];
        final isCorrect = userAnswer == question?.correctOption;
        return
            //  GestureDetector(
            //   onTap: () {
            //     Get.to(() => ShuffleQuizAnswersExplanation(
            //         question: question,
            //         selectedAnswers: selectedAnswers,
            //         quiz: shuffleQuizModel!));
            //   },
            //   child:
            buildShuffleQuestionContainer(question!, index,
                shuffleQuizModel?.questionsList?.length, isCorrect);
        // );
      },
    );
  }

  Widget buildQuestionContainer(
      QuestionModel? question, int index, int? totalQuestions, bool isCorrect) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Txt(
                  fontColor: grey2Color,
                  txt: "Pitanje ${index + 1} OD $totalQuestions",
                  fontSize: 14),
              const Spacer(),
              CircleAvatar(
                radius: 8,
                backgroundColor: isCorrect
                    ? correctAnswerBorderColor
                    : wrongAnswerBorderColor,
                child: const CircleAvatar(
                    backgroundColor: Colors.white, radius: 3),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Txt(txt: question?.question ?? '', maxLines: 10, fontSize: 16),
        ],
      ),
    );
  }

  Widget buildShuffleQuestionContainer(ShuffleQuizQuestionModel? question,
      int index, int? totalQuestions, bool isCorrect) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Txt(
                  fontColor: grey2Color,
                  txt: "Pitanje ${index + 1} OD $totalQuestions",
                  fontSize: 14),
              const Spacer(),
              CircleAvatar(
                radius: 8,
                backgroundColor: isCorrect
                    ? correctAnswerBorderColor
                    : wrongAnswerBorderColor,
                child: const CircleAvatar(
                    backgroundColor: Colors.white, radius: 3),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Txt(txt: question!.question!, maxLines: 10, fontSize: 16),
        ],
      ),
    );
  }
}
