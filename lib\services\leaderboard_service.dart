import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// ROBUST Group-based leaderboard service - 10 users max per GROUP
class LeaderboardService {
  static const List<String> _leagues = [
    '<PERSON>ronzan<PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>etist',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>kt<PERSON><PERSON><PERSON>',
    'Korals<PERSON>',
    'Jupiter',
    'Elit<PERSON>'
  ];

  static const int _maxPlayersPerGroup = 10;
  static const String _defaultLeague = 'Bronzana';

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _userId;

  LeaderboardService(this._userId);

  /// Find available group in a league (ATOMIC and ROBUST)
  Future<String> findAvailableGroup(String league) async {
    try {
      debugPrint('🔍 Finding available group in $league for user $_userId');

      // First check if user already has a group assignment
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      if (userDoc.exists) {
        final userData = userDoc.data()!;
        final existingGroupId = userData['groupId'] as String?;
        final existingLeague = userData['league'] as String?;

        if (existingGroupId != null && existingLeague == league) {
          // Check if that group still exists and has space
          final groupDoc = await _firestore
              .collection('leaderboards')
              .doc(league)
              .collection('groups')
              .doc(existingGroupId)
              .collection('players')
              .doc(_userId)
              .get();

          if (groupDoc.exists) {
            debugPrint(
                '   ✅ User already in $existingGroupId, keeping them there');
            return existingGroupId;
          }
        }
      }

      // Get all groups in the league
      final groupsSnapshot = await _firestore
          .collection('leaderboards')
          .doc(league)
          .collection('groups')
          .orderBy(FieldPath.documentId) // Consistent ordering
          .get();

      debugPrint('   Found ${groupsSnapshot.docs.length} existing groups');

      // Check each existing group for availability
      for (final groupDoc in groupsSnapshot.docs) {
        final playersSnapshot =
            await groupDoc.reference.collection('players').get();

        final playerCount = playersSnapshot.docs.length;
        debugPrint(
            '   Group ${groupDoc.id}: $playerCount/$_maxPlayersPerGroup players');

        if (playerCount < _maxPlayersPerGroup) {
          debugPrint(
              '   ✅ Using ${groupDoc.id} (has ${_maxPlayersPerGroup - playerCount} spaces)');
          return groupDoc.id;
        }
      }

      // All groups are full, create a new one
      final newGroupId = 'group_${groupsSnapshot.docs.length + 1}';
      debugPrint('   🆕 All groups full, creating $newGroupId');
      return newGroupId;
    } catch (e) {
      debugPrint('❌ Error finding available group: $e');
      return 'group_1'; // Safe fallback
    }
  }

  /// Add user to a specific group (ATOMIC OPERATION)
  Future<void> addUserToGroup({
    required String username,
    required String league,
    required String groupId,
  }) async {
    try {
      debugPrint('🎯 Adding user $_userId to $league/$groupId');

      // If no specific groupId provided, find an available one
      String finalGroupId = groupId;
      if (groupId.isEmpty) {
        finalGroupId = await findAvailableGroup(league);
      }

      // Validate username
      String finalUsername = username;
      if (username.isEmpty || username == 'Unknown') {
        finalUsername = await _generateUniqueUsername();
      }

      // ATOMIC TRANSACTION: Update both leaderboard and user profile
      await _firestore.runTransaction((transaction) async {
        // 1. Add to leaderboard group
        final playerRef = _firestore
            .collection('leaderboards')
            .doc(league)
            .collection('groups')
            .doc(finalGroupId)
            .collection('players')
            .doc(_userId);

        transaction.set(playerRef, {
          'username': finalUsername,
          'league': league,
          'groupId': finalGroupId,
          'weeklyScore': 0,
          'playerId': _userId,
          'lastUpdated': FieldValue.serverTimestamp(),
        });

        // 2. Update user profile
        final userRef = _firestore.collection('users').doc(_userId);
        transaction.update(userRef, {
          'league': league,
          'groupId': finalGroupId,
          'lastLeagueUpdate': FieldValue.serverTimestamp(),
        });
      });

      debugPrint('   ✅ Successfully added to $league/$finalGroupId');
    } catch (e) {
      debugPrint('❌ Failed to add user to group: $e');
      rethrow;
    }
  }

  /// Update user's weekly score (SAFE)
  Future<void> updateWeeklyScore(int scoreToAdd) async {
    try {
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      if (!userDoc.exists) {
        debugPrint('❌ User $_userId not found for score update');
        return;
      }

      final userData = userDoc.data()!;
      final currentLeague = userData['league'] as String? ?? _defaultLeague;
      final currentGroupId = userData['groupId'] as String? ?? 'group_1';

      // Update both leaderboard and user profile atomically
      await _firestore.runTransaction((transaction) async {
        // Update score in leaderboard group
        final playerRef = _firestore
            .collection('leaderboards')
            .doc(currentLeague)
            .collection('groups')
            .doc(currentGroupId)
            .collection('players')
            .doc(_userId);

        transaction.update(playerRef, {
          'weeklyScore': FieldValue.increment(scoreToAdd),
          'lastUpdated': FieldValue.serverTimestamp(),
        });
      });

      debugPrint('✅ Updated score +$scoreToAdd for user $_userId');
    } catch (e) {
      debugPrint('❌ Failed to update weekly score: $e');
    }
  }

  /// Get current group standings
  Future<List<Map<String, dynamic>>> getGroupStandings(
      String league, String groupId) async {
    try {
      final playersSnapshot = await _firestore
          .collection('leaderboards')
          .doc(league)
          .collection('groups')
          .doc(groupId)
          .collection('players')
          .orderBy('weeklyScore', descending: true)
          .orderBy('lastUpdated', descending: false)
          .limit(_maxPlayersPerGroup)
          .get();

      return playersSnapshot.docs
          .map((doc) => {
                'playerId': doc.id,
                'username': doc.data()['username'] ?? 'Unknown',
                'weeklyScore': doc.data()['weeklyScore'] ?? 0,
                'league': doc.data()['league'] ?? league,
                'groupId': doc.data()['groupId'] ?? groupId,
                'lastUpdated': doc.data()['lastUpdated'],
              })
          .toList();
    } catch (e) {
      debugPrint('❌ Failed to get group standings: $e');
      return [];
    }
  }

  /// Generate unique username
  Future<String> _generateUniqueUsername() async {
    try {
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      String baseName = 'user';

      if (userDoc.exists) {
        final userData = userDoc.data()!;
        baseName = (userData['name'] as String? ?? 'user')
            .toLowerCase()
            .replaceAll(' ', '');
      }

      int suffix = 1;
      String uniqueUsername;

      while (true) {
        uniqueUsername = '@$baseName-$suffix';

        final existing = await _firestore
            .collection('users')
            .where('uniqueName', isEqualTo: uniqueUsername)
            .get();

        if (existing.docs.isEmpty) {
          break;
        }
        suffix++;
      }

      // Update user's profile
      await _firestore.collection('users').doc(_userId).update({
        'uniqueName': uniqueUsername,
      });

      return uniqueUsername;
    } catch (e) {
      debugPrint('❌ Error generating username: $e');
      return '@user-${_userId.substring(0, 6)}';
    }
  }

  /// Static helper methods
  static List<String> get leagues => List.unmodifiable(_leagues);
  static String get defaultLeague => _defaultLeague;
  static int get maxPlayersPerGroup => _maxPlayersPerGroup;
}
