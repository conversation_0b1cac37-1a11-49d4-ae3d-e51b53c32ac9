name: bibl
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+10

environment:
  sdk: ">=3.2.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  app_links: ^6.3.3
  auto_size_text: ^3.0.0
  cached_network_image: ^3.4.1
  carousel_slider: ^5.0.0
  cloud_firestore: ^5.6.5
  connectivity_plus: ^6.1.3
  cupertino_icons: ^1.0.2
  device_preview: 1.1.0
  figma_to_flutter: ^0.0.6
  firebase_analytics: ^11.4.4
  firebase_auth: ^5.5.1
  firebase_core: ^3.12.1
  firebase_messaging: ^15.2.4
  firebase_storage: ^12.4.4
  flutter:
    sdk: flutter
  flutter_cache_manager: ^3.3.1
  flutter_card_swiper: ^7.0.2
  flutter_dotenv: ^5.1.0
  flutter_email_sender: ^6.0.2
  flutter_image_compress: ^2.3.0
  flutter_local_notifications: ^17.0.0
  flutter_native_splash: ^2.4.6
  flutter_rating_bar: ^4.0.1
  flutter_svg: ^2.0.7
  cloud_functions: ^5.4.0
  flutter_timezone: ^4.1.0
  get: ^4.6.6
  google_mobile_ads: ^4.0.0
  google_sign_in: ^6.2.1
  http: ^1.2.0
  image_picker: ^1.1.2
  internet_connection_checker: ^1.0.0+1
  intl: any
  just_audio: ^0.9.36
  path: ^1.8.3
  path_provider: ^2.1.4
  permission_handler: ^11.3.1
  pool: ^1.5.1
  purchases_flutter: ^8.6.0
  snowball_stemmer: any
  rxdart: ^0.27.7
  share_plus: any
  fuzzywuzzy: ^1.2.0
  shared_preferences: ^2.2.2
  shimmer: ^3.0.0
  sign_in_with_apple: ^6.1.4
  smooth_page_indicator: ^1.2.0+3
  sqflite: ^2.3.3+1
  stacked_card_carousel: ^0.0.4
  timezone: ^0.9.2
  url_launcher: ^6.2.5

dev_dependencies:
  flutter_lints: ^2.0.0
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/svgs/
    - assets/svgs/achievementsImages/
    - .env
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: SF Pro Text
      fonts:
        - asset: assets/fonts/SF-Pro-Text-Bold.otf
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages


flutter_native_splash:
  color: "#ffffff"

  android_12:
    color: "#ffffff"
    

